'use client'

import React, { useState, useMemo } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Upload, 
  Download, 
  Users, 
  AlertCircle, 
  CheckCircle2, 
  FileSpreadsheet,
  Calendar,
  Clock,
  MapPin,
  Settings,
  ChevronLeft,
  ChevronRight,
  Plus,
  Star,
  Search,
  Filter,
  X,
  CalendarDays,
  Copy,
  RotateCcw
} from 'lucide-react'
import * as XLSX from 'xlsx'
import toast from 'react-hot-toast'

interface SchedulingResult {
  岗位编码: string
  姓名: string
  工号: string
  技能等级: number
  班组: string
  工作中心: string
  日期: string
}

interface TaskData {
  产成品编码: string
  岗位编码: string
  需求人数: number
}

interface PositionData {
  工作中心: string
  岗位编码: string
  岗位技能等级: number
}

interface SkillMatrixData {
  姓名: string
  工号: string
  班组?: string
  [key: string]: any
}

interface PositionGroup {
  岗位编码: string
  岗位名称: string
  工作中心: string
  班组: string
  技能等级: string
  需求人数: number
  已排人数: number
  员工列表: SchedulingResult[]
}

interface FilterState {
  岗位编码: string
  工作中心: string
  班组: string
  状态: string // '全部' | '已满' | '缺员'
  搜索关键词: string
}

interface WeeklySchedule {
  [date: string]: {
    positionGroups: PositionGroup[]
    schedulingResults: SchedulingResult[]
    productCode: string
  }
}

type ViewMode = 'day' | 'week'

export default function PaibanPage() {
  const [skuFile, setSkuFile] = useState<File | null>(null)
  const [positionFile, setPositionFile] = useState<File | null>(null)
  const [skillFile, setSkillFile] = useState<File | null>(null)
  const [productCode, setProductCode] = useState('C1B010000036')
  const [schedulingResults, setSchedulingResults] = useState<SchedulingResult[]>([])
  const [positionGroups, setPositionGroups] = useState<PositionGroup[]>([])
  const [isProcessing, setIsProcessing] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [currentDate, setCurrentDate] = useState(new Date())
  const [activeTab, setActiveTab] = useState('upload')
  const [showFilters, setShowFilters] = useState(false)
  const [viewMode, setViewMode] = useState<ViewMode>('day')
  const [weeklySchedule, setWeeklySchedule] = useState<WeeklySchedule>({})
  const [selectedWeekDay, setSelectedWeekDay] = useState<string>('')
  
  // 筛选状态
  const [filters, setFilters] = useState<FilterState>({
    岗位编码: '',
    工作中心: '',
    班组: '',
    状态: '全部',
    搜索关键词: ''
  })

  // 获取当前周的日期范围
  const getCurrentWeekDates = () => {
    const today = new Date(currentDate)
    const dayOfWeek = today.getDay()
    const monday = new Date(today)
    monday.setDate(today.getDate() - (dayOfWeek === 0 ? 6 : dayOfWeek - 1))
    
    const weekDates = []
    for (let i = 0; i < 7; i++) {
      const date = new Date(monday)
      date.setDate(monday.getDate() + i)
      weekDates.push(date)
    }
    return weekDates
  }

  const weekDates = getCurrentWeekDates()

  // 获取筛选选项
  const filterOptions = useMemo(() => {
    let currentGroups = positionGroups
    
    if (viewMode === 'week' && selectedWeekDay) {
      currentGroups = weeklySchedule[selectedWeekDay]?.positionGroups || []
    }
    
    const 岗位编码列表 = [...new Set(currentGroups.map(g => g.岗位编码))].sort()
    const 工作中心列表 = [...new Set(currentGroups.map(g => g.工作中心))].sort()
    const 班组列表 = [...new Set(currentGroups.flatMap(g => g.员工列表.map(w => w.班组)).filter(Boolean))].sort()
    
    return {
      岗位编码列表,
      工作中心列表,
      班组列表
    }
  }, [positionGroups, weeklySchedule, selectedWeekDay, viewMode])

  // 筛选后的岗位组
  const filteredPositionGroups = useMemo(() => {
    let currentGroups = positionGroups
    
    if (viewMode === 'week' && selectedWeekDay) {
      currentGroups = weeklySchedule[selectedWeekDay]?.positionGroups || []
    }
    
    return currentGroups.filter(group => {
      // 岗位编码筛选
      if (filters.岗位编码 && !group.岗位编码.includes(filters.岗位编码)) {
        return false
      }
      
      // 工作中心筛选
      if (filters.工作中心 && group.工作中心 !== filters.工作中心) {
        return false
      }
      
      // 班组筛选
      if (filters.班组 && !group.员工列表.some(w => w.班组 === filters.班组)) {
        return false
      }
      
      // 状态筛选
      if (filters.状态 !== '全部') {
        const 已满 = group.已排人数 >= group.需求人数
        if (filters.状态 === '已满' && !已满) return false
        if (filters.状态 === '缺员' && 已满) return false
      }
      
      // 搜索关键词筛选
      if (filters.搜索关键词) {
        const keyword = filters.搜索关键词.toLowerCase()
        const searchText = [
          group.岗位编码,
          group.工作中心,
          ...group.员工列表.map(w => w.姓名),
          ...group.员工列表.map(w => w.工号)
        ].join(' ').toLowerCase()
        
        if (!searchText.includes(keyword)) {
          return false
        }
      }
      
      return true
    })
  }, [positionGroups, weeklySchedule, selectedWeekDay, viewMode, filters])

  // 重置筛选
  const resetFilters = () => {
    setFilters({
      岗位编码: '',
      工作中心: '',
      班组: '',
      状态: '全部',
      搜索关键词: ''
    })
  }

  // 检查是否有活动筛选
  const hasActiveFilters = useMemo(() => {
    return filters.岗位编码 || filters.工作中心 || filters.班组 || 
           filters.状态 !== '全部' || filters.搜索关键词
  }, [filters])

  const handleFileUpload = (file: File, setter: (file: File | null) => void) => {
    if (file && file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
      setter(file)
      setError(null)
    } else {
      toast.error('请上传 .xlsx 格式的文件')
    }
  }

  const readExcelFile = async (file: File): Promise<any[]> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target?.result as ArrayBuffer)
          const workbook = XLSX.read(data, { type: 'array' })
          const sheetName = workbook.SheetNames[0]
          const worksheet = workbook.Sheets[sheetName]
          const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })
          resolve(jsonData)
        } catch (error) {
          reject(error)
        }
      }
      reader.onerror = () => reject(new Error('文件读取失败'))
      reader.readAsArrayBuffer(file)
    })
  }

  const processSkuData = (rawData: any[]): TaskData[] => {
    return rawData.slice(1).map(row => ({
      产成品编码: row[0] || '',
      岗位编码: row[4] || '',
      需求人数: parseInt(row[16]) || 0
    })).filter(item => item.产成品编码 && item.岗位编码)
  }

  const processPositionData = (rawData: any[]): PositionData[] => {
    return rawData.slice(1).map(row => ({
      工作中心: row[2] || '',
      岗位编码: row[5] || '',
      岗位技能等级: parseInt(row[12]) || 0
    })).filter(item => item.工作中心 && item.岗位编码)
  }

  const processSkillMatrix = (rawData: any[]): SkillMatrixData[] => {
    if (rawData.length < 2) return []
    
    const headers = rawData[0]
    return rawData.slice(1).map(row => {
      const obj: SkillMatrixData = {
        姓名: '',
        工号: ''
      }
      
      headers.forEach((header: string, index: number) => {
        if (header === '姓名') obj.姓名 = row[index] || ''
        else if (header === '工号') obj.工号 = row[index] || ''
        else if (header === '班组') obj.班组 = row[index] || ''
        else obj[header] = parseInt(row[index]) || 0
      })
      
      return obj
    }).filter(item => item.姓名 && item.工号)
  }

  // 单日排班逻辑
  const performDayScheduling = async (targetDate: string, productCodeForDay: string) => {
    if (!skuFile || !positionFile || !skillFile || !productCodeForDay) {
      throw new Error('请上传所有数据文件，并填写SKU型号')
    }

    const [skuRawData, positionRawData, skillRawData] = await Promise.all([
      readExcelFile(skuFile),
      readExcelFile(positionFile),
      readExcelFile(skillFile)
    ])

    const table1 = processSkuData(skuRawData)
    const dolePositions = processPositionData(positionRawData)
    const skillMatrix = processSkillMatrix(skillRawData)

    //todayTasks表示处理过后的：产品编码（唯一）-岗位编码（唯一）-需求人数（总）
    /*     [
      { 产成品编码: 'A', 岗位编码: 'P1', 需求人数: 5 },  
      { 产成品编码: 'A', 岗位编码: 'P2', 需求人数: 1 }
    ] */
    const todayTasks = table1
      .filter(item => item.产成品编码 === productCodeForDay)
      .reduce((acc, curr) => {
        const existing = acc.find(item => item.岗位编码 === curr.岗位编码)
        if (existing) {
          existing.需求人数 += curr.需求人数
        } else {
          acc.push({ ...curr })
        }
        return acc
      }, [] as TaskData[])
      .filter(item => item.需求人数 > 0)

    // 获取已经在本周其他日期排班的员工
    const weeklyAssignedWorkers = new Set<string>()
    Object.entries(weeklySchedule).forEach(([date, schedule]) => {
      if (date !== targetDate) {
        schedule.schedulingResults.forEach(result => {
          weeklyAssignedWorkers.add(result.工号)
        })
      }
    })

    const assignedWorkers = new Set<string>(weeklyAssignedWorkers)
    const results: SchedulingResult[] = []
    const groups: PositionGroup[] = []

    for (const task of todayTasks) {
      const { 岗位编码: postCode, 需求人数: requiredPeople } = task
      // 查找岗位要求（第一个满足要求的）
      /* [
        {
          工作中心: "内装修线",
          岗位编码: "NZX-G010",
          岗位技能等级: 3
        },
        {
          工作中心: "内装修线",
          岗位编码: "NZX-G020",
          岗位技能等级: 2
        },
        ...
      ] */
      const skillReq = dolePositions.find(pos => pos.岗位编码 === postCode)
      if (!skillReq) continue

      const requiredSkillLevel = skillReq.岗位技能等级
      const workCenter = skillReq.工作中心
      /*       [
        {
          姓名: "张三",
          工号: "1001",
          班组: "A组",
          "NZX-G010": 3,
          "NZX-G020": 2,
          ...
        },
        {
          姓名: "李四",
          工号: "1002",
          班组: "B组",
          "NZX-G010": 0,
          "NZX-G020": 3,
          ...
        }
      ] */
      let available = skillMatrix.filter(worker => !assignedWorkers.has(worker.工号))
      let eligible = available.filter(worker => (worker[postCode] || 0) >= requiredSkillLevel)
      //找到了员工就停止（不管够不够）
      let downgrade = 0
      while (eligible.length === 0 && requiredSkillLevel - downgrade > 0) {
        downgrade += 1
        eligible = available.filter(worker => (worker[postCode] || 0) >= (requiredSkillLevel - downgrade))
        if (requiredSkillLevel - downgrade <= 1) break
      }

      eligible.sort((a, b) => {
        if (a.班组 && b.班组 && a.班组 !== b.班组) {
          return a.班组.localeCompare(b.班组)
        }
        return (b[postCode] || 0) - (a[postCode] || 0)
      })

      const assigned = eligible.slice(0, requiredPeople)
      const positionResults: SchedulingResult[] = []

      for (const worker of assigned) {
        const result = {
          岗位编码: postCode,
          姓名: worker.姓名,
          工号: worker.工号,
          技能等级: worker[postCode] || 0,
          班组: worker.班组 || '',
          工作中心: workCenter,
          日期: targetDate
        }
        results.push(result)
        positionResults.push(result)
        assignedWorkers.add(worker.工号)
      }

      groups.push({
        岗位编码: postCode,
        岗位名称: postCode,
        工作中心: workCenter,
        班组: positionResults[0]?.班组 || '',
        技能等级: `${requiredSkillLevel}级`,
        需求人数: requiredPeople,
        已排人数: positionResults.length,
        员工列表: positionResults
      })
      /*       {
        岗位编码: 'NZX-G010',        // 岗位的唯一编码
        岗位名称: 'NZX-G010',        // 岗位名称（这里用编码代替，也可以改为真实名称）
        工作中心: '内装修线',        // 岗位所属的工作中心
        班组: 'A',                  // 班组名称
        技能等级: '2级',             // 岗位要求的技能等级（原始需求等级）
        需求人数: 2,                // 岗位需求的人员数量
        已排人数: 2,                // 当前已排班的人员数量
        员工列表: [                 // 实际被排班的员工详细信息列表
          {
            岗位编码: 'NZX-G010',    // 员工对应的岗位编码
            姓名: '张三',            // 员工姓名
            工号: '001',             // 员工工号（唯一标识）
            技能等级: 3,            // 员工技能等级（实际技能，可能高于或等于岗位需求等级）
            班组: 'A',              // 员工所在班组
            工作中心: '内装修线',    // 员工所属工作中心
            日期: '2024-06-10'       // 排班日期
          },
          {
            岗位编码: 'NZX-G010',
            姓名: '李四',
            工号: '002',
            技能等级: 2,
            班组: 'A',
            工作中心: '内装修线',
            日期: '2024-06-10'
          }
        ]
      } */
    }

    return { results, groups }
  }

  // 单日排班
  const handleScheduling = async () => {
    setIsProcessing(true)
    setError(null)

    try {
      const targetDate = formatDate(currentDate)
      const { results, groups } = await performDayScheduling(targetDate, productCode)

      setSchedulingResults(results)
      setPositionGroups(groups)
      
      // 更新周排班数据
      setWeeklySchedule(prev => ({
        ...prev,
        [targetDate]: {
          positionGroups: groups,
          schedulingResults: results,
          productCode: productCode
        }
      }))
      
      setActiveTab('schedule')
      
      if (results.length > 0) {
        toast.success('排班成功！')
      } else {
        toast.error('无排班结果，请检查数据或条件')
      }
    } catch (error) {
      console.error('排班处理错误:', error)
      setError(error instanceof Error ? error.message : '处理文件时发生错误，请检查文件格式')
      toast.error('处理失败')
    } finally {
      setIsProcessing(false)
    }
  }

  // 一周排班
  const handleWeeklyScheduling = async () => {
    setIsProcessing(true)
    setError(null)

    try {
      const newWeeklySchedule: WeeklySchedule = {}
      
      for (const date of weekDates) {
        const dateStr = formatDate(date)
        const { results, groups } = await performDayScheduling(dateStr, productCode)
        
        newWeeklySchedule[dateStr] = {
          positionGroups: groups,
          schedulingResults: results,
          productCode: productCode
        }
      }

      setWeeklySchedule(newWeeklySchedule)
      setViewMode('week')
      setActiveTab('schedule')
      
      const totalResults = Object.values(newWeeklySchedule).reduce((sum, day) => sum + day.schedulingResults.length, 0)
      
      if (totalResults > 0) {
        toast.success(`一周排班成功！共排班 ${totalResults} 人次`)
      } else {
        toast.error('无排班结果，请检查数据或条件')
      }
    } catch (error) {
      console.error('一周排班处理错误:', error)
      setError(error instanceof Error ? error.message : '处理文件时发生错误，请检查文件格式')
      toast.error('一周排班失败')
    } finally {
      setIsProcessing(false)
    }
  }

  // 复制排班到其他日期
  const copyScheduleToDate = (fromDate: string, toDate: string) => {
    const sourceSchedule = weeklySchedule[fromDate]
    if (!sourceSchedule) return

    setWeeklySchedule(prev => ({
      ...prev,
      [toDate]: {
        ...sourceSchedule,
        schedulingResults: sourceSchedule.schedulingResults.map(result => ({
          ...result,
          日期: toDate
        }))
      }
    }))

    toast.success(`已复制 ${fromDate} 的排班到 ${toDate}`)
  }

  const downloadResults = () => {
    if (viewMode === 'day') {
      if (schedulingResults.length === 0) return

      const ws = XLSX.utils.json_to_sheet(schedulingResults)
      const wb = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(wb, ws, '今日排班结果')
      XLSX.writeFile(wb, `${formatDate(currentDate)}_排班结果.xlsx`)
      toast.success('文件下载成功')
    } else {
      // 下载一周的排班结果
      const allResults: SchedulingResult[] = []
      Object.values(weeklySchedule).forEach(day => {
        allResults.push(...day.schedulingResults)
      })

      if (allResults.length === 0) return

      const ws = XLSX.utils.json_to_sheet(allResults)
      const wb = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(wb, ws, '一周排班结果')
      
      // 添加每日汇总表
      const dailySummary = weekDates.map(date => {
        const dateStr = formatDate(date)
        const daySchedule = weeklySchedule[dateStr]
        return {
          日期: dateStr,
          产品SKU: daySchedule?.productCode || '',
          排班人数: daySchedule?.schedulingResults.length || 0,
          岗位数: daySchedule?.positionGroups.length || 0
        }
      })
      
      const summaryWs = XLSX.utils.json_to_sheet(dailySummary)
      XLSX.utils.book_append_sheet(wb, summaryWs, '每日汇总')
      
      XLSX.writeFile(wb, `${formatDate(weekDates[0])}_至_${formatDate(weekDates[6])}_一周排班结果.xlsx`)
      toast.success('一周排班文件下载成功')
    }
  }

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('zh-CN', { 
      year: 'numeric', 
      month: '2-digit',
      day: '2-digit'
    })
  }

  const formatWeekday = (date: Date) => {
    const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
    return weekdays[date.getDay()]
  }

  const navigateDate = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate)
    if (viewMode === 'week') {
      newDate.setDate(newDate.getDate() + (direction === 'next' ? 7 : -7))
    } else {
      newDate.setDate(newDate.getDate() + (direction === 'next' ? 1 : -1))
    }
    setCurrentDate(newDate)
  }

  const navigateToWeek = () => {
    setViewMode('week')
    setActiveTab('schedule')
  }

  const navigateToDay = (date: Date) => {
    setCurrentDate(date)
    setViewMode('day')
    
    const dateStr = formatDate(date)
    const daySchedule = weeklySchedule[dateStr]
    
    if (daySchedule) {
      setSchedulingResults(daySchedule.schedulingResults)
      setPositionGroups(daySchedule.positionGroups)
      setProductCode(daySchedule.productCode)
    }
    
    setActiveTab('schedule')
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航栏 */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h1 className="text-xl font-semibold text-gray-900">按岗位排班</h1>
            <Button variant="ghost" size="sm">
              <Settings className="h-4 w-4" />
            </Button>
          </div>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Button variant="ghost" size="sm" onClick={() => navigateDate('prev')}>
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <div className="flex items-center space-x-2 px-3 py-1 bg-gray-100 rounded-md">
                <Calendar className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium">{formatDate(currentDate)}</span>
              </div>
              <Button variant="ghost" size="sm" onClick={() => navigateDate('next')}>
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
            <Button className="bg-blue-600 hover:bg-blue-700">
              发布
            </Button>
          </div>
        </div>
      </div>

      <div className="p-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2 mb-6">
            <TabsTrigger value="upload" className="flex items-center gap-2">
              <Upload className="h-4 w-4" />
              数据上传
            </TabsTrigger>
            <TabsTrigger value="schedule" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              排班视图
            </TabsTrigger>
          </TabsList>

          <TabsContent value="upload" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 文件上传区域 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileSpreadsheet className="h-5 w-5" />
                    数据文件上传
                  </CardTitle>
                  <CardDescription>
                    请上传所需的Excel文件进行排班分析
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="sku-file">基础箱型库.xlsx</Label>
                    <div className="mt-1">
                      <Input
                        id="sku-file"
                        type="file"
                        accept=".xlsx"
                        onChange={(e) => e.target.files?.[0] && handleFileUpload(e.target.files[0], setSkuFile)}
                        className="cursor-pointer"
                      />
                      {skuFile && (
                        <Badge variant="secondary" className="mt-2">
                          <CheckCircle2 className="h-3 w-3 mr-1" />
                          {skuFile.name}
                        </Badge>
                      )}
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="position-file">岗位图谱.xlsx</Label>
                    <div className="mt-1">
                      <Input
                        id="position-file"
                        type="file"
                        accept=".xlsx"
                        onChange={(e) => e.target.files?.[0] && handleFileUpload(e.target.files[0], setPositionFile)}
                        className="cursor-pointer"
                      />
                      {positionFile && (
                        <Badge variant="secondary" className="mt-2">
                          <CheckCircle2 className="h-3 w-3 mr-1" />
                          {positionFile.name}
                        </Badge>
                      )}
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="skill-file">技能矩阵.xlsx</Label>
                    <div className="mt-1">
                      <Input
                        id="skill-file"
                        type="file"
                        accept=".xlsx"
                        onChange={(e) => e.target.files?.[0] && handleFileUpload(e.target.files[0], setSkillFile)}
                        className="cursor-pointer"
                      />
                      {skillFile && (
                        <Badge variant="secondary" className="mt-2">
                          <CheckCircle2 className="h-3 w-3 mr-1" />
                          {skillFile.name}
                        </Badge>
                      )}
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="product-code">产品SKU</Label>
                    <Input
                      id="product-code"
                      type="text"
                      placeholder="如：C1B010000036"
                      value={productCode}
                      onChange={(e) => setProductCode(e.target.value)}
                      className="mt-1"
                    />
                  </div>

                  {error && (
                    <Alert variant="destructive">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>{error}</AlertDescription>
                    </Alert>
                  )}

                  <div className="grid grid-cols-1 gap-3">
                    <Button
                      onClick={handleScheduling}
                      disabled={isProcessing}
                      className="w-full"
                      size="lg"
                    >
                      {isProcessing ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          处理中...
                        </>
                      ) : (
                        <>
                          <Users className="h-4 w-4 mr-2" />
                          今日排班
                        </>
                      )}
                    </Button>

                    <Button
                      onClick={handleWeeklyScheduling}
                      disabled={isProcessing}
                      variant="outline"
                      className="w-full"
                      size="lg"
                    >
                      {isProcessing ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                          处理中...
                        </>
                      ) : (
                        <>
                          <CalendarDays className="h-4 w-4 mr-2" />
                          一周排班
                        </>
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* 排班统计 */}
              <Card>
                <CardHeader>
                  <CardTitle>排班统计</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {viewMode === 'day' ? (
                      <>
                        <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                          <span className="text-sm text-gray-600">总岗位数</span>
                          <span className="text-lg font-semibold text-blue-600">{positionGroups.length}</span>
                        </div>
                        <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                          <span className="text-sm text-gray-600">已排人数</span>
                          <span className="text-lg font-semibold text-green-600">{schedulingResults.length}</span>
                        </div>
                        <div className="flex justify-between items-center p-3 bg-orange-50 rounded-lg">
                          <span className="text-sm text-gray-600">需求人数</span>
                          <span className="text-lg font-semibold text-orange-600">
                            {positionGroups.reduce((sum, group) => sum + group.需求人数, 0)}
                          </span>
                        </div>
                      </>
                    ) : (
                      <>
                        <div className="flex justify-between items-center p-3 bg-purple-50 rounded-lg">
                          <span className="text-sm text-gray-600">一周总排班</span>
                          <span className="text-lg font-semibold text-purple-600">
                            {Object.values(weeklySchedule).reduce((sum, day) => sum + day.schedulingResults.length, 0)} 人次
                          </span>
                        </div>
                        <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                          <span className="text-sm text-gray-600">已排天数</span>
                          <span className="text-lg font-semibold text-blue-600">
                            {Object.keys(weeklySchedule).length} / 7 天
                          </span>
                        </div>
                        <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                          <span className="text-sm text-gray-600">平均每日</span>
                          <span className="text-lg font-semibold text-green-600">
                            {Object.keys(weeklySchedule).length > 0 
                              ? Math.round(Object.values(weeklySchedule).reduce((sum, day) => sum + day.schedulingResults.length, 0) / Object.keys(weeklySchedule).length)
                              : 0
                            } 人
                          </span>
                        </div>
                      </>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="schedule" className="space-y-6">
            {/* 视图切换和导航 */}
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <Button
                    variant={viewMode === 'day' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewMode('day')}
                  >
                    <Calendar className="h-4 w-4 mr-2" />
                    日视图
                  </Button>
                  <Button
                    variant={viewMode === 'week' ? 'default' : 'outline'}
                    size="sm"
                    onClick={navigateToWeek}
                  >
                    <CalendarDays className="h-4 w-4 mr-2" />
                    周视图
                  </Button>
                </div>
                
                {viewMode === 'week' && (
                  <div className="text-sm text-gray-500">
                    {formatDate(weekDates[0])} - {formatDate(weekDates[6])}
                  </div>
                )}
              </div>
              
              <div className="flex items-center gap-2">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => setShowFilters(!showFilters)}
                  className={hasActiveFilters ? 'border-blue-500 text-blue-600' : ''}
                >
                  <Filter className="h-4 w-4 mr-2" />
                  筛选
                  {hasActiveFilters && (
                    <Badge variant="secondary" className="ml-2 h-5 w-5 p-0 flex items-center justify-center">
                      !
                    </Badge>
                  )}
                </Button>
                {((viewMode === 'day' && schedulingResults.length > 0) || 
                  (viewMode === 'week' && Object.keys(weeklySchedule).length > 0)) && (
                  <Button onClick={downloadResults} variant="outline">
                    <Download className="h-4 w-4 mr-2" />
                    导出Excel
                  </Button>
                )}
              </div>
            </div>

            {/* 周视图 - 日期选择器 */}
            {viewMode === 'week' && (
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-base">一周排班概览</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-7 gap-2">
                    {weekDates.map((date, index) => {
                      const dateStr = formatDate(date)
                      const daySchedule = weeklySchedule[dateStr]
                      const isSelected = selectedWeekDay === dateStr
                      const hasSchedule = !!daySchedule
                      
                      return (
                        <div key={index} className="text-center">
                          <div className="text-xs text-gray-500 mb-1">{formatWeekday(date)}</div>
                          <Button
                            variant={isSelected ? 'default' : hasSchedule ? 'secondary' : 'outline'}
                            size="sm"
                            className={`w-full h-16 flex flex-col items-center justify-center ${
                              hasSchedule && !isSelected ? 'bg-green-50 border-green-200 text-green-700' : ''
                            }`}
                            onClick={() => setSelectedWeekDay(isSelected ? '' : dateStr)}
                          >
                            <div className="text-sm font-medium">{date.getDate()}</div>
                            {hasSchedule && (
                              <div className="text-xs">
                                {daySchedule.schedulingResults.length}人
                              </div>
                            )}
                          </Button>
                          
                          {hasSchedule && (
                            <div className="mt-2 flex justify-center gap-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-6 w-6 p-0"
                                onClick={() => navigateToDay(date)}
                                title="查看详情"
                              >
                                <Search className="h-3 w-3" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-6 w-6 p-0"
                                onClick={() => {
                                  const otherDates = weekDates.filter(d => formatDate(d) !== dateStr)
                                  // 这里可以添加复制到其他日期的逻辑
                                }}
                                title="复制排班"
                              >
                                <Copy className="h-3 w-3" />
                              </Button>
                            </div>
                          )}
                        </div>
                      )
                    })}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* 筛选面板 */}
            {showFilters && (
              <Card>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-base">筛选条件</CardTitle>
                    <div className="flex items-center gap-2">
                      {hasActiveFilters && (
                        <Button variant="ghost" size="sm" onClick={resetFilters}>
                          <X className="h-4 w-4 mr-1" />
                          清除
                        </Button>
                      )}
                      <Button variant="ghost" size="sm" onClick={() => setShowFilters(false)}>
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                    {/* 搜索框 */}
                    <div>
                      <Label className="text-xs text-gray-500">搜索</Label>
                      <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                        <Input
                          placeholder="岗位、姓名、工号..."
                          value={filters.搜索关键词}
                          onChange={(e) => setFilters(prev => ({ ...prev, 搜索关键词: e.target.value }))}
                          className="pl-10"
                        />
                      </div>
                    </div>

                    {/* 岗位编码筛选 */}
                    <div>
                      <Label className="text-xs text-gray-500">岗位编码</Label>
                      <select
                        value={filters.岗位编码}
                        onChange={(e) => setFilters(prev => ({ ...prev, 岗位编码: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="">全部岗位</option>
                        {filterOptions.岗位编码列表.map(code => (
                          <option key={code} value={code}>{code}</option>
                        ))}
                      </select>
                    </div>

                    {/* 工作中心筛选 */}
                    <div>
                      <Label className="text-xs text-gray-500">工作中心</Label>
                      <select
                        value={filters.工作中心}
                        onChange={(e) => setFilters(prev => ({ ...prev, 工作中心: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="">全部中心</option>
                        {filterOptions.工作中心列表.map(center => (
                          <option key={center} value={center}>{center}</option>
                        ))}
                      </select>
                    </div>

                    {/* 班组筛选 */}
                    <div>
                      <Label className="text-xs text-gray-500">班组</Label>
                      <select
                        value={filters.班组}
                        onChange={(e) => setFilters(prev => ({ ...prev, 班组: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="">全部班组</option>
                        {filterOptions.班组列表.map(team => (
                          <option key={team} value={team}>{team}</option>
                        ))}
                      </select>
                    </div>

                    {/* 状态筛选 */}
                    <div>
                      <Label className="text-xs text-gray-500">排班状态</Label>
                      <select
                        value={filters.状态}
                        onChange={(e) => setFilters(prev => ({ ...prev, 状态: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="全部">全部状态</option>
                        <option value="已满">已满员</option>
                        <option value="缺员">缺员</option>
                      </select>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* 排班结果表格 */}
            {(viewMode === 'day' && positionGroups.length > 0) || 
             (viewMode === 'week' && selectedWeekDay && weeklySchedule[selectedWeekDay]) ? (
              <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50 border-b border-gray-200">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          岗位信息
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          技能
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          需求
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          已排人员
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {filteredPositionGroups.map((group, index) => (
                        <tr key={index} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div>
                              <div className="text-sm font-medium text-gray-900">{group.岗位编码}</div>
                              <div className="text-sm text-gray-500 flex items-center gap-1">
                                <MapPin className="h-3 w-3" />
                                {group.工作中心}
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <Badge variant="outline" className="text-xs">
                              {group.技能等级}
                            </Badge>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center gap-2">
                              <span className="text-sm font-medium">
                                {group.已排人数}/{group.需求人数}
                              </span>
                              <Badge 
                                variant={group.已排人数 >= group.需求人数 ? "default" : "destructive"}
                                className="text-xs"
                              >
                                {group.已排人数 >= group.需求人数 ? "已满" : `缺${group.需求人数 - group.已排人数}人`}
                              </Badge>
                            </div>
                          </td>
                          <td className="px-6 py-4">
                            <div className="flex flex-wrap gap-2">
                              {group.员工列表.map((worker, workerIndex) => (
                                <div 
                                  key={workerIndex} 
                                  className="inline-flex items-center gap-2 px-3 py-1 bg-blue-50 rounded-full text-xs"
                                >
                                  <div className="w-5 h-5 bg-blue-100 rounded-full flex items-center justify-center text-xs font-medium text-blue-600">
                                    {worker.姓名.charAt(0)}
                                  </div>
                                  <span className="font-medium">{worker.姓名}</span>
                                  <span className="text-gray-500">({worker.工号})</span>
                                  <div className="flex items-center gap-1">
                                    <Star className="h-3 w-3 text-yellow-400 fill-current" />
                                    <span>{worker.技能等级}</span>
                                  </div>
                                </div>
                              ))}
                              {group.已排人数 < group.需求人数 && (
                                <div className="inline-flex items-center gap-1 px-3 py-1 border-2 border-dashed border-gray-300 rounded-full text-xs text-gray-400">
                                  <Plus className="h-3 w-3" />
                                  <span>还需{group.需求人数 - group.已排人数}人</span>
                                </div>
                              )}
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                
                {filteredPositionGroups.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p>没有找到符合条件的岗位</p>
                    <Button variant="ghost" size="sm" onClick={resetFilters} className="mt-2">
                      清除筛选条件
                    </Button>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-12 text-gray-500 bg-white rounded-lg border border-gray-200">
                <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p className="text-lg font-medium">
                  {viewMode === 'week' ? '请选择日期查看排班详情' : '暂无排班结果'}
                </p>
                <p className="text-sm">
                  {viewMode === 'week' ? '点击上方日期卡片查看具体排班' : '请先上传文件并进行排班'}
                </p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
